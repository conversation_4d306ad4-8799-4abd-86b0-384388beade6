# Makefile for Non-Inferiority Trial LaTeX Document
# Cross-platform build recipe for pdflatex compilation with bibliography

# Variables
MAIN = non_inferiority_mathematical_basis
TEX_FILE = $(MAIN).tex
PDF_FILE = $(MAIN).pdf
BIB_FILE = references.bib

# LaTeX compiler settings
LATEX = pdflatex
BIBTEX = bibtex
LATEX_FLAGS = -interaction=nonstopmode -file-line-error

# Auxiliary files to clean
AUX_FILES = $(MAIN).aux $(MAIN).log $(MAIN).bbl $(MAIN).blg \
            $(MAIN).toc $(MAIN).out $(MAIN).fls $(MAIN).fdb_latexmk \
            $(MAIN).synctex.gz $(MAIN).nav $(MAIN).snm $(MAIN).vrb \
            $(MAIN).alg $(MAIN).lof $(MAIN).lot

# Default target
.PHONY: all clean help open

all: $(PDF_FILE)

# Main build rule: pdflatex -> bibtex -> pdflatex -> pdflatex
$(PDF_FILE): $(TEX_FILE) $(BIB_FILE)
	@echo "========================================="
	@echo " Building Non-Inferiority Trial Document"
	@echo "========================================="
	@echo "[1/4] Running initial pdflatex compilation..."
	$(LATEX) $(LATEX_FLAGS) $(TEX_FILE)
	@echo "[2/4] Processing bibliography with bibtex..."
	-$(BIBTEX) $(MAIN)
	@echo "[3/4] Running second pdflatex compilation..."
	$(LATEX) $(LATEX_FLAGS) $(TEX_FILE)
	@echo "[4/4] Running final pdflatex compilation..."
	$(LATEX) $(LATEX_FLAGS) $(TEX_FILE)
	@echo "========================================="
	@echo " BUILD COMPLETED SUCCESSFULLY"
	@echo "========================================="
	@echo "PDF generated: $(PDF_FILE)"
	@ls -lh $(PDF_FILE) 2>/dev/null || dir $(PDF_FILE) 2>NUL || echo "PDF file created"

# Clean auxiliary files
clean:
	@echo "Cleaning auxiliary files..."
	@rm -f $(AUX_FILES) 2>/dev/null || del $(AUX_FILES) 2>NUL || true
	@echo "Cleanup completed."

# Clean everything including PDF
clean-all: clean
	@echo "Removing PDF file..."
	@rm -f $(PDF_FILE) 2>/dev/null || del $(PDF_FILE) 2>NUL || true
	@echo "All files cleaned."

# Open the generated PDF
open: $(PDF_FILE)
	@echo "Opening PDF file..."
ifeq ($(OS),Windows_NT)
	@start $(PDF_FILE)
else ifeq ($(shell uname -s),Darwin)
	@open $(PDF_FILE)
else
	@xdg-open $(PDF_FILE) || evince $(PDF_FILE) || okular $(PDF_FILE) || echo "Please install a PDF viewer"
endif

# Force rebuild
rebuild: clean all

# Help target
help:
	@echo "Available targets:"
	@echo "  all       - Build the PDF document (default)"
	@echo "  clean     - Remove auxiliary files"
	@echo "  clean-all - Remove all generated files including PDF"
	@echo "  open      - Open the generated PDF"
	@echo "  rebuild   - Clean and build from scratch"
	@echo "  help      - Show this help message"
	@echo ""
	@echo "Usage examples:"
	@echo "  make           # Build the document"
	@echo "  make clean     # Clean auxiliary files"
	@echo "  make open      # Open the PDF after building"
	@echo "  make rebuild   # Force complete rebuild"

# Dependencies
$(TEX_FILE):
	@echo "Error: $(TEX_FILE) not found!"
	@exit 1

$(BIB_FILE):
	@echo "Warning: $(BIB_FILE) not found. Bibliography may not be processed."