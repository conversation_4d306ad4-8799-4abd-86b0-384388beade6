# Mathematical Basis of Non-Inferiority Trial Data Analysis

A comprehensive LaTeX document covering the mathematical foundations, statistical methods, and practical implementation of non-inferiority (NI) trial data analysis.

## Project Overview

This document provides a complete mathematical framework for non-inferiority trials, covering:

- **Theoretical Foundations**: Hypothesis formulation, parameter estimation, and mathematical framework
- **Statistical Methods**: Farrington-Manning method, confidence interval approaches, score tests
- **Practical Implementation**: Sample size calculations, analysis populations, missing data handling
- **Advanced Topics**: Adaptive designs, Bayesian approaches, regulatory considerations
- **Worked Examples**: Step-by-step calculations and real-world applications

The document is structured as an academic paper suitable for publication or educational purposes, with comprehensive mathematical derivations and practical guidance.

## Prerequisites

### LaTeX Distribution
You need a complete LaTeX distribution installed on your system:

**Windows:**
- [MiKTeX](https://miktex.org/) (recommended) or [TeX Live](https://tug.org/texlive/)

**macOS:**
- [MacTeX](https://tug.org/mactex/) (includes TeX Live)

**Linux:**
- TeX Live (usually available through package managers)
  ```bash
  # Ubuntu/Debian
  sudo apt-get install texlive-full
  
  # Fedora/CentOS
  sudo dnf install texlive-scheme-full
  ```

### Required LaTeX Packages

The document uses the following packages (most are included in full LaTeX distributions):

**Core Packages:**
- `amsmath`, `amssymb`, `amsfonts` - Mathematical symbols and environments
- `geometry` - Page layout and margins
- `graphicx` - Graphics and image inclusion
- `natbib` - Bibliography management
- `hyperref` - PDF hyperlinks and bookmarks

**Additional Packages:**
- `booktabs` - Professional table formatting
- `array` - Enhanced array and table environments
- `fancyhdr` - Custom headers and footers
- `enumerate` - Enhanced enumeration environments
- `algorithm`, `algorithmic` - Algorithm formatting
- `xcolor` - Color support

**Font and Input:**
- `inputenc` - Input encoding (UTF-8)
- `fontenc` - Font encoding

## File Structure

```
non-inferiority/
├── non_inferiority_mathematical_basis.tex    # Main LaTeX document
├── references.bib                            # Bibliography file
├── compile.bat                               # Windows compilation script
├── Makefile                                  # Cross-platform build file
├── README.md                                 # This file
└── non_inferiority_mathematical_basis.pdf   # Generated PDF (after compilation)
```

### File Descriptions

- **`non_inferiority_mathematical_basis.tex`**: The main LaTeX document containing all content, mathematical formulations, and formatting
- **`references.bib`**: BibTeX bibliography file with comprehensive references to foundational papers, regulatory guidelines, and methodological advances
- **`compile.bat`**: Windows batch script for automated compilation with proper bibliography processing
- **`README.md`**: Complete documentation and usage instructions

## Compilation Instructions

### Option 1: Automated Compilation (Windows)

1. **Double-click** `compile.bat` or run from command prompt:
   ```cmd
   compile.bat
   ```

2. The script will automatically:
   - Run pdflatex three times
   - Process bibliography with bibtex
   - Clean up auxiliary files
   - Display compilation status

3. If successful, you'll find `non_inferiority_mathematical_basis.pdf` in the project directory.

### Option 2: Manual Compilation

For any operating system, use these commands in sequence:

```bash
# Step 1: Initial compilation
pdflatex non_inferiority_mathematical_basis.tex

# Step 2: Process bibliography
bibtex non_inferiority_mathematical_basis

# Step 3: Resolve citations
pdflatex non_inferiority_mathematical_basis.tex

# Step 4: Final compilation for cross-references
pdflatex non_inferiority_mathematical_basis.tex
```

### Option 3: Cross-Platform Makefile

For Unix-like systems (Linux, macOS) or Windows with Make installed:

```bash
# Build the document
make

# Clean auxiliary files
make clean

# Open the generated PDF
make open

# Force rebuild
make rebuild

# Show all available targets
make help
```

### Option 4: Using LaTeX Editors

**Overleaf (Online):**
1. Create new project and upload all files
2. Set main document to `non_inferiority_mathematical_basis.tex`
3. Click "Recompile"

**TeXmaker/TeXstudio:**
1. Open `non_inferiority_mathematical_basis.tex`
2. Use "Quick Build" or configure custom build sequence
3. Ensure bibliography processing is enabled

**VS Code with LaTeX Workshop:**
1. Install LaTeX Workshop extension
2. Open the `.tex` file
3. Use Ctrl+Alt+B to build

## Troubleshooting

### Common Issues and Solutions

**1. "Package not found" errors:**
```
Solution: Update your LaTeX distribution or install missing packages
- MiKTeX: Use MiKTeX Package Manager or enable automatic package installation
- TeX Live: Use tlmgr package manager
```

**2. Bibliography not appearing:**
```
Solution: Ensure proper compilation sequence
1. Run pdflatex
2. Run bibtex
3. Run pdflatex twice more
```

**3. "Undefined references" warnings:**
```
Solution: Run pdflatex multiple times to resolve cross-references
The automated script handles this automatically.
```

**4. PDF not generated:**
```
Solution: Check the .log file for specific errors
- Look for "! " at the beginning of lines for error messages
- Common issues: missing packages, syntax errors, file permissions
```

**5. Compilation hangs or stops:**
```
Solution: 
- Use -interaction=nonstopmode flag for pdflatex
- Check for infinite loops in cross-references
- Ensure file paths don't contain spaces or special characters
```

### Error Log Analysis

Key error indicators in `.log` files:
- `! LaTeX Error:` - LaTeX-specific errors
- `! Undefined control sequence` - Missing commands or packages
- `! File not found` - Missing files or incorrect paths
- `Overfull \hbox` - Text overflow (warnings, usually non-critical)

## Customization

### Modifying Content

**Adding Sections:**
```latex
\section{Your New Section}
Your content here with proper LaTeX formatting.

\subsection{Subsection}
More detailed content.
```

**Mathematical Equations:**
```latex
% Inline math
The parameter $\theta$ represents...

% Display equations
\begin{equation}
\delta = p_C - p_E
\label{eq:your_label}
\end{equation}

% Reference equations
As shown in Equation \eqref{eq:your_label}...
```

**Adding References:**
1. Add entries to `references.bib`:
```bibtex
@article{your_reference_2024,
  title={Your Paper Title},
  author={Author, First and Author, Second},
  journal={Journal Name},
  volume={1},
  pages={1--10},
  year={2024}
}
```

2. Cite in text:
```latex
\citep{your_reference_2024}  % (Author, 2024)
\citet{your_reference_2024}  % Author (2024)
```

### Formatting Options

**Page Layout:**
```latex
\usepackage[margin=1.5in]{geometry}  % Adjust margins
\usepackage{setspace}
\doublespacing  % Double spacing
```

**Font Options:**
```latex
\usepackage{times}        % Times New Roman
\usepackage{palatino}     % Palatino
\usepackage{helvet}       % Helvetica
```

**Color Scheme:**
```latex
\usepackage{xcolor}
\definecolor{myblue}{RGB}{0,50,150}
\hypersetup{colorlinks=true,linkcolor=myblue}
```

## Document Content Overview

### Main Sections

1. **Introduction** - Overview and motivation for NI trials
2. **Mathematical Framework** - Hypothesis formulation and parameter estimation
3. **Statistical Methods** - Farrington-Manning, confidence intervals, score tests
4. **Sample Size Calculations** - Power analysis for different endpoint types
5. **Analysis Populations** - ITT vs. Per-Protocol considerations
6. **Missing Data and Sensitivity** - Handling incomplete data
7. **Practical Implementation** - Algorithms and decision rules
8. **Worked Examples** - Step-by-step calculations
9. **Advanced Topics** - Adaptive designs, Bayesian methods, platform trials
10. **Regulatory Considerations** - ICH guidelines, FDA/EMA requirements
11. **Quality Control** - Validation and monitoring procedures
12. **Future Directions** - Emerging methods and technologies

### Mathematical Coverage

- **Binary Endpoints**: Farrington-Manning method, exact methods
- **Continuous Endpoints**: t-tests, ANCOVA approaches
- **Time-to-Event**: Cox regression, Kaplan-Meier methods
- **Confidence Intervals**: Multiple approaches and comparisons
- **Sample Size**: Power calculations for all endpoint types
- **Missing Data**: MAR, MNAR, sensitivity analyses

## Output Description

The compiled PDF contains:

- **Professional formatting** with proper typography and layout
- **Comprehensive table of contents** with hyperlinked sections
- **Mathematical equations** with proper numbering and referencing
- **Algorithm boxes** for practical implementation
- **Worked examples** with step-by-step calculations
- **Complete bibliography** with 50+ references
- **Cross-references** linking equations, sections, and citations
- **Hyperlinked PDF** for easy navigation

**Typical output specifications:**
- **Pages**: ~50-60 pages
- **Word count**: ~25,000 words
- **Equations**: ~100+ numbered equations
- **References**: All entries from bibliography (45+ sources including foundational papers, regulatory guidelines, and methodological advances)
- **Figures/Tables**: Algorithm boxes and summary tables

**Note on PDF Generation:**
The PDF file (`non_inferiority_mathematical_basis.pdf`) is generated locally during compilation and is not included in source control. This follows standard practices for LaTeX projects where source files are maintained and PDFs are generated as needed. To create the PDF, run the compilation script or follow the manual compilation instructions above.

## Dependencies and Versions

### LaTeX Version Compatibility
- **Minimum**: LaTeX2e (1994 or later)
- **Tested**: TeX Live 2020-2024, MiKTeX 2.9+
- **Recommended**: Latest version of your chosen distribution

### Package Versions
Most packages are stable across versions. Critical dependencies:
- `amsmath` v2.14+ (for advanced mathematical environments)
- `hyperref` v6.83+ (for PDF bookmarks and links)
- `natbib` v8.31+ (for bibliography formatting)

### Compilation Time
- **First compile**: 2-5 minutes (depending on system)
- **Subsequent compiles**: 30-60 seconds
- **Full automated script**: 3-7 minutes

## Version Information

- **Document Version**: 1.0
- **Last Updated**: 2024
- **LaTeX Standard**: LaTeX2e
- **Bibliography Style**: plainnat (author-year citations)

## Support and Maintenance

### Getting Help

1. **LaTeX Errors**: Check the comprehensive `.log` file generated during compilation
2. **Package Issues**: Consult CTAN (https://ctan.org/) for package documentation
3. **Compilation Problems**: Use the troubleshooting section above

### Updates and Modifications

The document is designed to be easily maintainable:
- **Modular structure** allows easy section additions/modifications
- **Consistent formatting** through LaTeX class and package options
- **Comprehensive commenting** in the source code
- **Standardized mathematical notation** using custom commands

### Best Practices

When modifying the document:
1. **Test compilation frequently** to catch errors early
2. **Use consistent notation** following the established conventions
3. **Add appropriate references** for new content
4. **Maintain the academic tone** and professional formatting
5. **Validate mathematical formulas** before inclusion

## Academic and Professional Use

This document is suitable for:
- **Educational purposes** in biostatistics and clinical trial methodology courses
- **Research reference** for statisticians working on NI trials
- **Regulatory submissions** requiring detailed statistical methodology
- **Publication** in peer-reviewed journals (with appropriate modifications)
- **Training materials** for pharmaceutical and clinical research organizations

The comprehensive coverage and rigorous mathematical treatment make it valuable for both theoretical understanding and practical implementation of non-inferiority trial methods.