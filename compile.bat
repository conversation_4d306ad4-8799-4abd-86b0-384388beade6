@echo off
REM Batch script for compiling LaTeX document to PDF
REM This script handles the complete LaTeX compilation workflow including bibliography processing

echo =====================================
echo  Non-Inferiority Trial LaTeX Compiler
echo =====================================
echo.

REM Set the main LaTeX file name (without extension)
set MAIN_FILE=non_inferiority_mathematical_basis

echo Starting compilation of %MAIN_FILE%.tex...
echo.

REM Step 1: Initial pdflatex run
echo [Step 1/5] Running initial pdflatex compilation...
pdflatex -interaction=nonstopmode %MAIN_FILE%.tex
if errorlevel 1 (
    echo ERROR: Initial pdflatex compilation failed!
    echo Check the .log file for details.
    goto :error
)
echo Initial compilation completed successfully.
echo.

REM Step 2: Generate bibliography with bibtex
echo [Step 2/5] Processing bibliography with bibtex...
bibtex %MAIN_FILE%
if errorlevel 1 (
    echo WARNING: Bibtex processing encountered issues.
    echo This might be normal if there are no citations yet.
)
echo Bibliography processing completed.
echo.

REM Step 3: Second pdflatex run to resolve citations
echo [Step 3/5] Running second pdflatex compilation (resolving citations)...
pdflatex -interaction=nonstopmode %MAIN_FILE%.tex
if errorlevel 1 (
    echo ERROR: Second pdflatex compilation failed!
    echo Check the .log file for details.
    goto :error
)
echo Second compilation completed successfully.
echo.

REM Step 4: Final pdflatex run to resolve all cross-references
echo [Step 4/5] Running final pdflatex compilation (resolving cross-references)...
pdflatex -interaction=nonstopmode %MAIN_FILE%.tex
if errorlevel 1 (
    echo ERROR: Final pdflatex compilation failed!
    echo Check the .log file for details.
    goto :error
)
echo Final compilation completed successfully.
echo.

REM Step 5: Clean up auxiliary files
echo [Step 5/5] Cleaning up auxiliary files...
if exist %MAIN_FILE%.aux del %MAIN_FILE%.aux
if exist %MAIN_FILE%.log del %MAIN_FILE%.log
if exist %MAIN_FILE%.bbl del %MAIN_FILE%.bbl
if exist %MAIN_FILE%.blg del %MAIN_FILE%.blg
if exist %MAIN_FILE%.toc del %MAIN_FILE%.toc
if exist %MAIN_FILE%.out del %MAIN_FILE%.out
if exist %MAIN_FILE%.fls del %MAIN_FILE%.fls
if exist %MAIN_FILE%.fdb_latexmk del %MAIN_FILE%.fdb_latexmk
if exist %MAIN_FILE%.synctex.gz del %MAIN_FILE%.synctex.gz
if exist %MAIN_FILE%.nav del %MAIN_FILE%.nav
if exist %MAIN_FILE%.snm del %MAIN_FILE%.snm
if exist %MAIN_FILE%.vrb del %MAIN_FILE%.vrb
if exist %MAIN_FILE%.alg del %MAIN_FILE%.alg
if exist %MAIN_FILE%.lof del %MAIN_FILE%.lof
if exist %MAIN_FILE%.lot del %MAIN_FILE%.lot
echo Cleanup completed.
echo.

REM Check if PDF was successfully generated
if exist %MAIN_FILE%.pdf (
    echo =====================================
    echo  COMPILATION SUCCESSFUL!
    echo =====================================
    echo.
    echo PDF file generated: %MAIN_FILE%.pdf
    echo File size: 
    dir %MAIN_FILE%.pdf | find /v "Directory"
    echo.
    echo You can now open the PDF file to view the compiled document.
    echo.
) else (
    echo ERROR: PDF file was not generated!
    echo Please check the compilation log for errors.
    goto :error
)

REM Option to open the PDF automatically
echo Do you want to open the PDF now? (Y/N)
set /p choice=Enter your choice: 
if /i "%choice%"=="Y" (
    echo Opening PDF...
    start %MAIN_FILE%.pdf
)

echo.
echo Compilation process completed successfully.
echo Press any key to exit...
pause >nul
exit /b 0

:error
echo.
echo =====================================
echo  COMPILATION FAILED!
echo =====================================
echo.
echo Please check the following:
echo 1. Ensure you have a LaTeX distribution installed (MiKTeX, TeX Live)
echo 2. Check that all required packages are installed
echo 3. Review the .log file for specific error messages
echo 4. Verify that the .tex file exists and is valid
echo.
echo Common solutions:
echo - Update your LaTeX distribution
echo - Install missing packages using your package manager
echo - Check for syntax errors in the .tex file
echo.
echo Press any key to exit...
pause >nul
exit /b 1